#include "file_batch.h"
#include <string>
#include <queue>
#include "file_batch_struct.h"
#include "mappingfile.h"
#include <time.h>

#define DEFAULT_TELCO 99

CGroupInfo groupInfo;

typedef std::queue<CGroupInfo> GroupInfoQueue;

EXEC SQL INCLUDE sendgrpfetchrec.h;
EXEC SQL INCLUDE batch_db_v1.h;


int getSendGroupInfo(sql_context db ,CSndGroupInfo& sndGroupInfo);
int setMsgData(sql_context db,struct sqlca sqlca,
        CMappingFile& mappingfile,
        CGroupInfo& groupInfo,
        CSndGroupInfo& sndGroupInfo,
        int lineCount);

int getTelcoId(char* szTelco,char* szDstaddr,int nMsgType);
char* cutString(char* szOrg, int leng);

void runBatch(CGroupInfo& groupInfo);
int getIdCode (sql_context db, CGroupInfo& groupInfo);

int main(int argc, char* argv[])
{
    int ret;
    CBatchDbV1 util;
    CGroupInfo groupInfo;
//    queue<string> qGroup;
//    queue<CGroupInfo> qGroup;
    GroupInfoQueue qGroup;
    struct sqlca sqlca;

    EXEC SQL BEGIN DECLARE SECTION;
    sql_context db;
    EXEC SQL END DECLARE SECTION;

    time_t ThisT, LastT;

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    Init_Server_Fork();

    monitoring("START [%s]",PROCESS_NAME);


    /* initServer check  */
    ret = configParse((char*)argv[1]);
    if( ret != 0 )
    {
        exit(1);
    }

    EXEC SQL ENABLE THREADS;
    EXEC SQL CONTEXT ALLOCATE :db;

    ret = Init_Oracle(db,sqlca);
    if( ret !=0 )
    {
        monitoring("db connection fail error sqlcode[%d]",ret);
        ml_sub_end();
        return -1;
    }

    while(activeProcess)
    {
        wait_a_moment(99999);

        time(&ThisT);

        if( difftime(ThisT,LastT) > 15 )
        {
            sndCheck2(PROCESS_NO, 63);
            time(&LastT);
        }

        memset(&groupInfo,0x00,sizeof(CGroupInfo));

        ret = util.getDataGroup(db,sqlca,groupInfo,qGroup);
        if( ret == 0 ) {
            wait_a_moment(gConf.dbNoDataSleep,0);
            continue;
        }

        if( ret < 0 )
        {
            monitoring("ORA-ERR:getDataGroup:sqlcode[%d]",ret);
            break;
        }

        // get data 
        /*printf("[%d][%s]\n",
                groupInfo.f_grp_id,
                groupInfo.file_name); 20210408 block */


        ret = util.upGroupStart(db,sqlca,groupInfo);
        if( ret != 0 )
        {
            monitoring("ORA-ERR:upGroupStart:sqlcode[%d]",ret);
            break;
        }


        // fork process
        int childPid;

        childPid = fork();
        if( childPid < 0 )
        {
            // Error
            monitoring("fork ERR:[%s]",strerror(errno));
            break;
        }

        if( childPid == 0 )
        { // child Process

            runBatch(groupInfo);

            exit(0);
        }
        // parent Process

    }

    EXEC SQL CONTEXT USE :db;
    EXEC SQL COMMIT WORK RELEASE;
    EXEC SQL CONTEXT FREE :db;


    monitoring("END [%s]",PROCESS_NAME);

    ml_sub_end();

    return 0;
}



int Init_Oracle(sql_context ctx,struct sqlca sqlca)
{
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR Username[32];
    VARCHAR Password[32];
    VARCHAR dbstring[32];
    EXEC SQL END DECLARE SECTION;

    strcpy((char*)Username.arr, gConf.dbID);
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, gConf.dbPASS);
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, gConf.dbSID);
    dbstring.len = strlen((char*)dbstring.arr);

#if (DEBUG >= 5)
	printf("gConf.dbID [%s] gConf.dbPASS[%s] \n", gConf.dbID, gConf.dbPASS);
	printf("gConf.dbSID [%s] \n", gConf.dbSID);
#endif

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
    return sqlca.sqlcode;
}

int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
    //    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    //conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),10);
    conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),20);
    if( gConf.dbID == NULL ) strcpy(gConf.dbID,"");
    //conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),10);
    conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),20);
    if( gConf.dbPASS == NULL ) strcpy(gConf.dbPASS,"");
    conf.strncpy2(gConf.dbSID , conf.FetchEntry("db.sid"),32);
    if( gConf.dbSID == NULL ) strcpy(gConf.dbSID,"");
    conf.strncpy2(gConf.filePath , conf.FetchEntry("file.path"),64);
    if( gConf.filePath == NULL ) strcpy(gConf.filePath,"");

    gConf.telcoDefault = conf.FetchEntryInt("telco.default");
    gConf.telcoCallbackDefault = conf.FetchEntryInt("telco.callback.default");
    gConf.dbNoDataSleep = conf.FetchEntryInt("db.nodatasleep");
    gConf.maxMappingCount = conf.FetchEntryInt("max.mappingcount");
    gConf.telcoRcs = conf.FetchEntryInt("telco.rcs");
	gConf.telcoNtk = conf.FetchEntryInt("telco.ntk");

    return 0;
}   

/*
 * nEndingFlag 
 * 0 succ
 * -1 db connection fail
 * -2 file open fail
 * 1 중지 요청 
 * -3 디비 에러 
 */
void runBatch(CGroupInfo& groupInfo)
{
    memset(&sqlca,0x00,sizeof(sqlca));
    CSndGroupInfo sndGroupInfo;
    CMappingFile mappingfile;

    int ret;
    int nEndingFlag =0;
   
    pthread_mutex_t runtime_mutex;

    EXEC SQL BEGIN DECLARE SECTION;
    sql_context db;
    int f_grp_id;
    int snd_seq;

    int fileLineCnt;
    int nTotalCnt=0;
    int nSuccCnt=0;
    int nFailCnt=0;
    EXEC SQL END DECLARE SECTION;


    EXEC SQL ENABLE THREADS;
    EXEC SQL CONTEXT ALLOCATE :db;
    	
    char szMbaseId[40+1];
    memset(szMbaseId,0x00,sizeof(szMbaseId));	

    pthread_mutex_init(&runtime_mutex,NULL);

    monitoring("START grpid[%d] snd_seq[%d] pid[%d]",
            groupInfo.f_grp_id,
            groupInfo.snd_seq,
            getpid());

    logmessage("START grpid[%d] snd_seq[%d] ptnid[%d] priority[%d] jobcode[%d] msg_type[%d] msg_cnt[%d] "
		"telco_info[%s] reg_date[%s] file_name[%s] work_cnt[%d]",
        groupInfo.f_grp_id,
        groupInfo.snd_seq,
        groupInfo.ptn_id,
        groupInfo.priority,
        groupInfo.job_code,
        groupInfo.msg_type,
        groupInfo.msg_cnt,
        groupInfo.telco_info,
        groupInfo.reg_date,
        trim(groupInfo.file_name,sizeof(groupInfo.file_name)),
        groupInfo.work_cnt);

    f_grp_id = groupInfo.f_grp_id;
    snd_seq = groupInfo.snd_seq;

    ret = Init_Oracle(db,sqlca);
    if( ret !=0 )
    {
        monitoring("db connection fail error sqlcode[%d] grp_id[%d]",
                ret, groupInfo.f_grp_id  );
        activeProcess = false;
        nEndingFlag = -1;
    }

    // file check
    fileLineCnt = groupInfo.work_cnt;
    trim(groupInfo.file_name,sizeof(groupInfo.file_name));
	
    ret = mappingfile.open(groupInfo.file_name,groupInfo.work_cnt,gConf.maxMappingCount);
	
//    ret = access(groupInfo.file_name,R_OK);

    if( ret!=0 ) {
        monitoring("[%s] file open ERR [%s]",  groupInfo.file_name,  strerror(errno));
        
        activeProcess = false;
        nEndingFlag = -2;
    }

    monitoring("open file [%s]",groupInfo.file_name);

    memset(&sndGroupInfo,0x00,sizeof(CSndGroupInfo));
    sndGroupInfo.nFGrpId = groupInfo.f_grp_id;

	// 식별코드 get
	ret = getIdCode(db, groupInfo);

    while(activeProcess)
    {
        sndGroupInfo.nTotal = nTotalCnt;

        ret = getSendGroupInfo(db,sndGroupInfo);
        
        logmessage("[SHS]getSendGroupInfo [%d][%d]", groupInfo.f_grp_id,ret);
        
        if( ret != 0 )
        {
            monitoring("getSendGroupInfo ERR: ret[%d]",ret);
            nEndingFlag = -3;
            break;
        }

        if( sndGroupInfo.nFlag != 9 )
        {
            monitoring("close : flag [%d]",sndGroupInfo.nFlag);
            nEndingFlag = 1;
            break;
        }
		// mappingFile.getData("0","n","1","Y","2","<@3@> - <@10@>",","))

		logmessage("%s:%d pthread_mutex_lock\n",__func__,__LINE__);

		pthread_mutex_lock(&runtime_mutex);
		
        ret = mappingfile.getData(
                sndGroupInfo.szDstInd,
                sndGroupInfo.szCallYn,
                sndGroupInfo.szCallInd,
                sndGroupInfo.szResvDataYn,
                sndGroupInfo.szResvData,
                sndGroupInfo.szSendMsg,
                sndGroupInfo.szToken,
                groupInfo.f_grp_id
                );

        //printf("mappingfile succ \n");
		pthread_mutex_unlock(&runtime_mutex);

		logmessage("%s:%d pthread_mutex_unlock\n",__func__,__LINE__);
        
        logmessage("[SHS]mappingfile getData[%d][%d]", groupInfo.f_grp_id,ret);

		fileLineCnt++;

        if( ret == -1 ) /* data file 1 row error */
        {
            // fail 
            EXEC SQL CONTEXT USE :db;
            EXEC SQL UPDATE TBL_RESERV_F_GRP 
                SET WORK_CNT = WORK_CNT + 1, FAIL_CNT = FAIL_CNT + 1 
                WHERE F_GRP_ID = :f_grp_id
				AND MSG_TYPE IN (0,1);

            nFailCnt++;
            nTotalCnt++;

            monitoring("mapping fail grp_id[%d] line[%d]",
                    groupInfo.f_grp_id,
                    fileLineCnt
                    );
            continue;
        }

        if( ret == 0 ) /* end of file */
            break;

        if( ret != 1 )
            break;

        ret = setMsgData(db,sqlca,mappingfile,groupInfo,sndGroupInfo,fileLineCnt);
		
		//memcpy(szMbaseId,mappingfile.getMbaseId() ,40);
		snprintf(szMbaseId, sizeof(szMbaseId), "%s", mappingfile.getMbaseId());
		trim(szMbaseId,strlen(szMbaseId));

		int nChannel = 0;
		nChannel = atoi(mappingfile.getChannel());

        if( ret == 0 )
        {
        	//if(atoi(mappingfile.getChannel()) == 6 || atoi(mappingfile.getChannel()) == 7 || atoi(mappingfile.getChannel()) == 9)
			// alimtalk
			if(nChannel == 6 || nChannel == 7 || nChannel == 9)
			{
				EXEC SQL CONTEXT USE :db;
            	EXEC SQL UPDATE TBL_RESERV_F_GRP 
            	    SET WORK_CNT = WORK_CNT + 1, ATK_SUCC_CNT = ATK_SUCC_CNT + 1 
            	    WHERE F_GRP_ID = :f_grp_id
					AND MSG_TYPE IN (0,1);
					
				if( sqlca.sqlcode != 0 )
       			{
            		monitoring("[ERR]alimtalk cnt update sqlcode[%d]",sqlca.sqlcode);
            		
            		break;
        		}
        		monitoring("[INF]alimtalk cnt update grpid[%d]",f_grp_id);	
        	}
			// Naver Talk
			else if(nChannel == 11)
			{
				EXEC SQL CONTEXT USE :db;
            	EXEC SQL UPDATE TBL_RESERV_F_GRP 
            	    SET WORK_CNT = WORK_CNT + 1, NTK_SUCC_CNT = NTK_SUCC_CNT + 1 
            	    WHERE F_GRP_ID = :f_grp_id
					AND MSG_TYPE IN (0,1);
					
				if( sqlca.sqlcode != 0 )
       			{
            		monitoring("[ERR] Navertalk cnt update sqlcode[%d]",sqlca.sqlcode);
            		
            		break;
        		}
        		monitoring("[INF] Navertalk cnt update grpid[%d]",f_grp_id);	
        	}
        	else if(nChannel == 8)
        	{
        		EXEC SQL CONTEXT USE :db;
            	EXEC SQL UPDATE TBL_RESERV_F_GRP 
            	    SET WORK_CNT = WORK_CNT + 1, WTK_SUCC_CNT = WTK_SUCC_CNT + 1 
            	    WHERE F_GRP_ID = :f_grp_id
					AND MSG_TYPE IN (0,1);
					
				if( sqlca.sqlcode != 0 )
       			{
            		monitoring("[ERR]wibee cnt update sqlcode[%d]",sqlca.sqlcode);
            		
            		break;
        		}
        		monitoring("[INF]wibee cnt update grpid[%d]",f_grp_id);			
        	}
        	else
        	{
        		if(nChannel == 5)
        		{
        			// fail 
            		EXEC SQL CONTEXT USE :db;
           			EXEC SQL UPDATE TBL_RESERV_F_GRP 
                		SET WORK_CNT = WORK_CNT + 1, FAIL_CNT = FAIL_CNT + 1 
                		WHERE F_GRP_ID = :f_grp_id
						AND MSG_TYPE IN (0,1);
				
					if( sqlca.sqlcode != 0 )
       				{
            			monitoring("[ERR]toss cnt update sqlcode[%d]",sqlca.sqlcode);
            		
            			break;
        			}
        			monitoring("[INF]toss cnt update grpid[%d]",f_grp_id);	
        		}
        		else
        		{
					// RCS
        			if((strncmp(mappingfile.getCallBack(),"15884000",12) == 0 || 
						strncmp(mappingfile.getCallBack(),"15889955",12) == 0) && strlen(szMbaseId) > 0 )
        			{
        				// succ
            			EXEC SQL CONTEXT USE :db;
            			EXEC SQL UPDATE TBL_RESERV_F_GRP 
            				SET WORK_CNT = WORK_CNT + 1, RCS_SUCC_CNT = RCS_SUCC_CNT + 1 
            			    WHERE F_GRP_ID = :f_grp_id
							AND MSG_TYPE IN (0,1);
							
						if( sqlca.sqlcode != 0 )
       					{
            				monitoring("[ERR]rcs cnt update sqlcode[%d]",sqlca.sqlcode);
            				
            				break;
        				}
        				monitoring("[INF]rcs cnt update grpid[%d]",f_grp_id);	
        			}
					// SMS
        			else
        			{
        				// succ
            			EXEC SQL CONTEXT USE :db;
            			EXEC SQL UPDATE TBL_RESERV_F_GRP 
            				SET WORK_CNT = WORK_CNT + 1, SUCC_CNT = SUCC_CNT + 1 
            			    WHERE F_GRP_ID = :f_grp_id
							AND MSG_TYPE IN (0,1);
							
						if( sqlca.sqlcode != 0 )
       					{
            				monitoring("[ERR]sms cnt update sqlcode[%d]",sqlca.sqlcode);
            				
            				break;
        				}
        				monitoring("[INF]sms cnt update grpid[%d]",f_grp_id);	
        			}	
        		}	
        	}
        	
            nSuccCnt++;
            nTotalCnt++;
        } 
		else if ( ret > 0 ) {
            // fail 
            EXEC SQL CONTEXT USE :db;
            EXEC SQL UPDATE TBL_RESERV_F_GRP 
                SET WORK_CNT = WORK_CNT + 1, FAIL_CNT = FAIL_CNT + 1 
                WHERE F_GRP_ID = :f_grp_id
				AND MSG_TYPE IN (0,1);
				
			if( sqlca.sqlcode != 0 )
       		{
            	monitoring("[ERR]fail cnt update sqlcode[%d]",sqlca.sqlcode);
            		
            	break;
        	}
        			
            nFailCnt++;
            nTotalCnt++;
            
            monitoring("[INF]fail cnt update grpid[%d]",f_grp_id);	
        }
		else {
            break;
            // error
            nEndingFlag = -3;
            monitoring("[INF]do not cnt update grpid[%d]",f_grp_id);
        }

        EXEC SQL CONTEXT USE :db;
        EXEC SQL COMMIT;

        wait_a_moment( 1000000 / sndGroupInfo.nCntSec );
    }

//    EXEC SQL CONTEXT USE :db;
    
    switch(nEndingFlag) {
       case 0: /* 완료 */
            EXEC SQL CONTEXT USE :db;
            EXEC SQL UPDATE TBL_RESERV_F_GRP_SND 
                SET WORK_TYPE = 'E' , END_DATE = SYSDATE, END_LINE = :fileLineCnt,
                    SUCC_CNT = :nSuccCnt , FAIL_CNT =  :nFailCnt
                WHERE SND_SEQ = :snd_seq;
                	
            if( sqlca.sqlcode != 0 )
       		{
            	monitoring("[ERR]work type end update sqlcode[%d]",sqlca.sqlcode);
            		
            	break;
        	}    	

            EXEC SQL CONTEXT USE :db;
            EXEC SQL UPDATE TBL_RESERV_F_GRP
                SET END_DATE = SYSDATE , FLAG_PRO = 1
                WHERE F_GRP_ID = :f_grp_id
				AND MSG_TYPE IN (0,1);
			
			if( sqlca.sqlcode != 0 )
       		{
            	monitoring("[ERR]flag_pro 1 update sqlcode[%d]",sqlca.sqlcode);
            		
            	break;
        	}    	
        	
        	monitoring("[INF]end status update grpid[%d]snd_seq[%d]",f_grp_id,snd_seq);

            /* file rename */
            //char szFileName[64];
            //char szFileName[128];
            //char szNewFileName[64];
            //char szNewFileName[700];
            //int i;
            /*for( i=strlen(groupInfo.file_name);i>0;i--)
            {
                if( groupInfo.file_name[i] == '/' )
                    break;
            }*/
            
            //strcpy(szFileName,groupInfo.file_name+i+1);
            //snprintf(szFileName, sizeof(szFileName), "%s", groupInfo.file_name+i+1);
            //sprintf(szNewFileName,"%s/%s_",gConf.filePath,szFileName);
            //snprintf(szNewFileName, sizeof(szNewFileName), "%s/%s_",gConf.filePath,szFileName);

            //ret = rename(groupInfo.file_name,szNewFileName );
            //monitoring("file rename ret [%d] 0:succ [%s]",ret,strerror(errno));
				
			ret = remove(groupInfo.file_name);
				
			if (ret ==0){
       		   monitoring("file remove success ret [%d]",ret);
            }
            else{
          		monitoring("file remove fail ret [%d] strerror[%s]",ret,strerror(errno));
         	 }

            break;

       case -2: /* data file open Error */
       case 1: /* 중지 요청 */
            EXEC SQL CONTEXT USE :db;
            EXEC SQL UPDATE TBL_RESERV_F_GRP_SND 
                SET WORK_TYPE = 'P' , END_DATE = SYSDATE, END_LINE = :fileLineCnt,
                    SUCC_CNT = :nSuccCnt , FAIL_CNT =  :nFailCnt
                WHERE SND_SEQ = :snd_seq;
                	
            if( sqlca.sqlcode != 0 )
       		{
            	monitoring("[ERR]work_type pause update sqlcode[%d]",sqlca.sqlcode);
            		
            	break;
        	}        	

            EXEC SQL CONTEXT USE :db;
            EXEC SQL UPDATE TBL_RESERV_F_GRP
                SET END_DATE = SYSDATE , FLAG_PRO = 3
                WHERE F_GRP_ID = :f_grp_id
				AND MSG_TYPE IN (0,1);
				
			if( sqlca.sqlcode != 0 )
       		{
            	monitoring("[ERR]flag_pro 3 update sqlcode[%d]",sqlca.sqlcode);
            		
            	break;
        	}   
        	
        	monitoring("[INF]pause status update grpid[%d]snd_seq[%d]",f_grp_id,snd_seq);

            break;

        default:
            monitoring("EndingFlag [%d]",nEndingFlag);
            break;
    }

    mappingfile.close();
    monitoring("END grpid[%d]",groupInfo.f_grp_id);
    logmessage("END grpid[%d]",groupInfo.f_grp_id);
    EXEC SQL CONTEXT USE :db;
    EXEC SQL COMMIT WORK RELEASE;
    EXEC SQL CONTEXT FREE :db;

    return ;
}

/** @brief  tbl_reserv_f_grp  테이블 정보 갱신
 *
 */
int getSendGroupInfo(sql_context db ,CSndGroupInfo& sndGroupInfo)
{

    if( (sndGroupInfo.nTotal % 100) == 0 )
    { // add time check
        // re get snd info

        EXEC SQL BEGIN DECLARE SECTION;
        int nFlag,nPriority, nJobCode, nMsgType,nCntSec, nGetDataCnt , nWorkCnt;
        VARCHAR szTelcoInfo[30];
        VARCHAR szSendMsg[256];
        VARCHAR szResvDataYn[1];
        VARCHAR szResvData[12];
        VARCHAR szToken[4];
        VARCHAR szDstInd[16];
        VARCHAR szCallYn[1];
        VARCHAR szCallInd[16];

        int nFGrpId;

        EXEC SQL END DECLARE SECTION;

//        CCL(szTelcoInfo);
//        CCL(szSendMsg);
//        CCL(szResvDataYn);
//        CCL(szResvData);
//        CCL(szToken);
        memset(&sqlca,0x00,sizeof(sqlca));

        nFGrpId = sndGroupInfo.nFGrpId;

        EXEC SQL CONTEXT USE :db;
        EXEC SQL SELECT 
            FLAG_PRO, PRIORITY, JOB_CODE, CNTPSEC, GETDATA_CNT, TELCO_INFO, WORK_CNT,MSG_TYPE,
            SEND_MSG, RESV_DATA_YN, RESV_DATA , TOKEN , DSTADDR , CALLBACK_YN , CALLBACK
        INTO
            nFlag, nPriority, nJobCode, nCntSec, nGetDataCnt, szTelcoInfo, nWorkCnt,nMsgType,
            szSendMsg, szResvDataYn, szResvData, szToken, szDstInd, szCallYn, szCallInd
        FROM 
            TBL_RESERV_F_GRP
        WHERE
            F_GRP_ID =:nFGrpId
		AND MSG_TYPE IN (0,1);

        if( sqlca.sqlcode != 0 )
        {
            monitoring("ORA-ERR:[%d][%d]",sqlca.sqlcode,nFGrpId);
            return -1;
        }

        sndGroupInfo.nFlag = nFlag;
        sndGroupInfo.nPriority = nPriority;
        sndGroupInfo.nJobCode = nJobCode;
        sndGroupInfo.nMsgType = nMsgType;
        sndGroupInfo.nCntSec = nCntSec;
        sndGroupInfo.nGetDataCnt = nGetDataCnt;
        sndGroupInfo.nWorkCnt = nWorkCnt;

        memcpy(sndGroupInfo.szTelcoInfo,(char*)szTelcoInfo.arr,szTelcoInfo.len);
        memcpy(sndGroupInfo.szSendMsg,(char*)szSendMsg.arr,szSendMsg.len);
        memcpy(sndGroupInfo.szResvDataYn,(char*)szResvDataYn.arr,szResvDataYn.len);
        memcpy(sndGroupInfo.szResvData,(char*)szResvData.arr,szResvData.len);
        memcpy(sndGroupInfo.szToken,(char*)szToken.arr,szToken.len);
        memcpy(sndGroupInfo.szDstInd,(char*)szDstInd.arr,szDstInd.len);
        memcpy(sndGroupInfo.szCallYn,(char*)szCallYn.arr,szCallYn.len);
        memcpy(sndGroupInfo.szCallInd,(char*)szCallInd.arr,szCallInd.len);
    } // end if re get snd info

    return 0;
}

int getIdCode (sql_context db, CGroupInfo& groupInfo)
{
	EXEC SQL BEGIN DECLARE SECTION;
	VARCHAR szIdCode[12];
	int nPtnId;
	EXEC SQL END DECLARE SECTION;
	
	memset(&sqlca,0x00,sizeof(sqlca));
	//memset(szIdCode,	0x00,	sizeof(szIdCode));
	
	nPtnId = groupInfo.ptn_id;
	
	EXEC SQL CONTEXT USE :db;
	EXEC SQL SELECT	
		ID_CODE
	INTO	szIdCode
	FROM TBL_PTN_INFO
	WHERE PTN_ID = :nPtnId;
		
	if( sqlca.sqlcode != 0 )
    {
        monitoring("ORA-ERR:[%d] nPtnId[%d]",sqlca.sqlcode, nPtnId);
        return -1;
    }
	
	memcpy(groupInfo.id_code, (char *)szIdCode.arr, szIdCode.len);

    logmessage("[getIdCode] groupInfo.id_code [%s] ptn_id [%d]", groupInfo.id_code, nPtnId);
	
	return 0;
}

int setMsgData(sql_context db,struct sqlca sqlca,
        CMappingFile& mappingfile,
        CGroupInfo& groupInfo,
        CSndGroupInfo& sndGroupInfo,
        int lineCount)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szTelco[36];
	int nCnt = -1;
	char szSqlErrorMsg[1024];
	int nmPID;
	char szPID[8];
	char szJOB[8];
	int nPID;
	int nJOB;

	int otResult = -1;
	char szSerial[16+1];
	char szDstadr[12+1];
	char szCalbck[12+1];
	//char szSndmsg[88+1];
	//char szSndmsg[86+1];
	char szSndmsg[90+1];
	//char szSndmsg2[88+1];
	//char szSndmsg2[86+1];
	char szSndmsg2[90+1];
	char szReserve[200+1];
	char szCharge[1+1];
	char szChannel[2+1];
	char szTmplCd[10+1];
	int nPRT;
	int nTelco;
	int nMsgId;
	int nRcsTelco;
	int nNtkTelco;
	long long nRcsId;
	long long nNtkId;
	char szMbaseId[40+1];
	char szRcsGroupId[20+1];
	char szHead[1+1];
	char szFooter[20+1];
	char szJob[3+1];
	char szTranPr[16+1];
	char szAtalkmsg[2000+1];
	char szRcsmsg[2600+1];
	char szReplacemsg[90+1];
	char szReplacemsg2[90+1];
	char szSenderKey[40+1];
	char szReqDate[14+1];
	char szButton[2000+1];
	//char szTitle[50+1];
	// Item Highlight 변경 50 - 200
	char szTitle[200+1];
	char szIdCode[12+1];

	EXEC SQL END DECLARE SECTION;

	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;

	struct timespec tmv;
	struct tm	tp;

	memset(szSndmsg     , 0x00, sizeof(szSndmsg     ));    
	memset(szSndmsg2    , 0x00, sizeof(szSndmsg2    ));
	memset(szReserve    , 0x00, sizeof(szReserve    ));
	memset(szDstadr     , 0x00, sizeof(szDstadr     ));
	memset(szCalbck     , 0x00, sizeof(szCalbck     ));
	memset(szSerial     , 0x00, sizeof(szSerial     ));
	memset(szTelco      , 0x00, sizeof(szTelco      ));
	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szPID        , 0x00, sizeof(szPID        ));
	memset(szCharge     , 0x00, sizeof(szCharge     ));
	memset(szChannel    , 0x00, sizeof(szChannel    ));
	memset(szTmplCd     , 0x00, sizeof(szTmplCd     ));
	memset(szJOB        , 0x00, sizeof(szJOB        ));
	memset(szJob        , 0x00, sizeof(szJob        ));
	memset(szTranPr     , 0x00, sizeof(szTranPr     ));
	memset(szAtalkmsg   , 0x00, sizeof(szAtalkmsg   ));
	memset(szReplacemsg , 0x00, sizeof(szReplacemsg ));
	memset(szReplacemsg2, 0x00, sizeof(szReplacemsg2));
	memset(szSenderKey  , 0x00, sizeof(szSenderKey  ));
	memset(szReqDate    , 0x00, sizeof(szReqDate    ));
	memset(szButton     , 0x00, sizeof(szButton     ));
	memset(szTitle      , 0x00, sizeof(szTitle      ));
	memset(szRcsmsg     , 0x00, sizeof(szRcsmsg     ));
	memset(szMbaseId    , 0x00, sizeof(szMbaseId    ));
	memset(szRcsGroupId , 0x00, sizeof(szRcsGroupId ));
	memset(szHead       , 0x00, sizeof(szHead       ));
	memset(szFooter     , 0x00, sizeof(szFooter     ));
	memset(szIdCode     , 0x00, sizeof(szIdCode     ));

	nPID = groupInfo.ptn_id;

	nJOB = sndGroupInfo.nJobCode;

	//strcpy(szTelco, sndGroupInfo.szTelcoInfo); 20210408
	//strncpy(szTelco, sndGroupInfo.szTelcoInfo,sizeof(szTelco)-1);
	snprintf(szTelco, sizeof(szTelco), "%s", sndGroupInfo.szTelcoInfo);

	// 식별코드 : 202311
	//strncpy(szIdCode, groupInfo.id_code, sizeof(szIdCode)-1);
	snprintf(szIdCode, sizeof(szIdCode), "%s", groupInfo.id_code);

	if( nJOB == -99 )
		return -1;

	//sprintf(szJob,"%d",nJOB); 20210408    
	snprintf(szJob,sizeof(szJob),"%d",nJOB);     

	nTelco = getTelcoId(szTelco,mappingfile.getDstAddr(),sndGroupInfo.nMsgType);

	//sprintf(szHead,"%s","0"); 20210408
	snprintf(szHead,sizeof(szHead),"%s","0");

	nRcsTelco = gConf.telcoRcs;
	nNtkTelco = gConf.telcoNtk;

	//printf("nRcsTelco[%d]",nRcsTelco);
	//logmessage("[SHS]setMsgData start1[%d]",groupInfo.f_grp_id);

	//CCL(szSqlErrorMsg);
	//nPRT = sndGroupInfo.nPriority;

	if(strlen(mappingfile.getCharge()) > 0)
	{
		memcpy(szCharge,mappingfile.getCharge(), 1);
		nPRT = atoi(szCharge);
	}
	else
	{
		nPRT = sndGroupInfo.nPriority;
	}		
	// ptn_sn = grpid_해당 파일의 라인 수 
	//sprintf(szSerial,"%d_%d",groupInfo.snd_seq,lineCount); 20210408
	snprintf(szSerial,sizeof(szSerial),"%d_%d",groupInfo.snd_seq,lineCount);
	//memcpy(szDstadr,mappingfile.getDstAddr(),12);
	snprintf(szDstadr, sizeof(szDstadr), "%s", mappingfile.getDstAddr());
	//memcpy(szCalbck,mappingfile.getCallBack(),12);
	snprintf(szCalbck, sizeof(szCalbck), "%s", mappingfile.getCallBack());

	//memcpy(szReserve,mappingfile.getResv(), 200);
	snprintf(szReserve, sizeof(szReserve), "%s", mappingfile.getResv());

	if(strlen(mappingfile.getChannel()) > 0)
	{
		//memcpy(szChannel,mappingfile.getChannel(), 1); 20210408
		//memcpy(szChannel,mappingfile.getChannel(), 2);
		snprintf(szChannel, sizeof(szChannel), "%s", mappingfile.getChannel());
		trim(szChannel,strlen(szChannel));
	}

	int nChannel = 0;
	nChannel = atoi(szChannel);

	logmessage("[%s:%d] szChannel[%.*s] nChannel[%d] ", __func__, __LINE__, 
			sizeof(szChannel), szChannel, nChannel);

	//if(atoi(szChannel) >= 0 && atoi(szChannel) <= 5)
	if(nChannel >= 0 && nChannel <= 5)
	{
		if(strcmp(szCalbck,"15884000") == 0 || strcmp(szCalbck,"15889955") == 0)
		{
			//memcpy(szMbaseId,mappingfile.getMbaseId() ,40);
			snprintf(szMbaseId, sizeof(szMbaseId), "%s", mappingfile.getMbaseId() );
			trim(szMbaseId,strlen(szMbaseId));
		}
	}

	//if(atoi(szChannel) == 6 || atoi(szChannel) == 7 || atoi(szChannel) == 8|| atoi(szChannel) == 9)
	if(nChannel == 6 || nChannel == 7 || nChannel == 8|| nChannel == 9 ||
		nChannel == 11)
	{
		//memcpy(szTmplCd,mappingfile.getTmplCd(), 10);
		snprintf(szTmplCd, sizeof(szTmplCd), "%s", mappingfile.getTmplCd());

		//memcpy(szReplacemsg2,mappingfile.getReplaceMsg() ,88);
		//memcpy(szReplacemsg2,mappingfile.getReplaceMsg() ,90);
		snprintf(szReplacemsg2, sizeof(szReplacemsg2), "%s", mappingfile.getReplaceMsg() );

		//strcpy(szReplacemsg,cutString(szReplacemsg2,88));
		//strcpy(szReplacemsg,cutString(szReplacemsg2,90));
		snprintf(szReplacemsg, sizeof(szReplacemsg), "%s", cutString(szReplacemsg2,90));

		//memcpy(szSenderKey,mappingfile.getSendKey() ,40);
		snprintf(szSenderKey, sizeof(szSenderKey), "%s", mappingfile.getSendKey());

		//memcpy(szAtalkmsg,mappingfile.getAtalkMsg(), 2000);
		snprintf(szAtalkmsg, sizeof(szAtalkmsg), "%s", mappingfile.getAtalkMsg());
	}
	else
	{	
		if((strcmp(szCalbck,"15884000") == 0 || strcmp(szCalbck,"15889955") == 0) && strlen(szMbaseId) > 0)
		{
			//memcpy(szReplacemsg2,mappingfile.getReplaceMsg() ,90);
			snprintf(szReplacemsg2, sizeof(szReplacemsg2), "%s", mappingfile.getReplaceMsg() );

			//strcpy(szReplacemsg,cutString(szReplacemsg2,90));
			snprintf(szReplacemsg, sizeof(szReplacemsg), "%s", cutString(szReplacemsg2,90));

			//memcpy(szRcsmsg,mappingfile.getRcsMsg(), 2600);
			snprintf(szRcsmsg, sizeof(szRcsmsg), "%s", mappingfile.getRcsMsg());
		}
		else
		{
			//memcpy(szSndmsg2,mappingfile.getMsg() ,88);
			//memcpy(szSndmsg2,mappingfile.getMsg() ,90);
			snprintf(szSndmsg2, sizeof(szSndmsg2), "%s", mappingfile.getMsg() );

			//strcpy(szSndmsg,cutString(szSndmsg2,88));
			//strcpy(szSndmsg,cutString(szSndmsg2,90));
			snprintf(szSndmsg, sizeof(szSndmsg), "%s", cutString(szSndmsg2,90));
		}
	}

	logmessage("[SHS]setMsgData start1[%d][%s]",groupInfo.f_grp_id,szSerial);

	//if(atoi(szChannel) == 6 || atoi(szChannel) == 7 || atoi(szChannel) == 9)
	// alimtalk : 679 / navartalk : 11
	if(nChannel == 6 || nChannel == 7 || nChannel == 9 ||
		nChannel == 11)
	{
		//memcpy(szButton,mappingfile.getButton(), sizeof(szButton)-1);
		snprintf(szButton, sizeof(szButton), "%s", mappingfile.getButton() );

		//memcpy(szTitle,mappingfile.getTitle(), sizeof(szTitle)-1);
		snprintf(szTitle, sizeof(szTitle), "%s", mappingfile.getTitle() );
	}

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(szReqDate, "%04d%02d%02d%02d%02d%02d"
			, tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday
			, tp.tm_hour, tp.tm_min, tp.tm_sec
	       );

	otResult = -999;

	if(sndGroupInfo.nMsgType == 0 ) {

		//if(atoi(szChannel) == 6 || atoi(szChannel) == 7|| atoi(szChannel) == 9)
		// 알림톡 : 7
		if(nChannel == 6 || nChannel == 7|| nChannel == 9)   
		{
			EXEC SQL CONTEXT USE :db;
			EXEC SQL EXECUTE
				BEGIN
				//proc_set_atalk_msgdata(
				proc_set_atalk_msgdata_var(
					dstaddr         =>  :szDstadr,
					callback        =>  :szCalbck,
					msg_body        =>  :szAtalkmsg,
					resv_data       =>  :szReserve,
					ptn_sn          =>  :szSerial,	
					job_code        =>  :szJob,
					charge          =>	:szCharge,
					tmplCd          =>	:szTmplCd,
					replace_msg     =>	:szReplacemsg,
					telco_id        =>  :nTelco ,
					sender_key      =>  :szSenderKey ,
					button          =>  :szButton ,	
					title           =>  :szTitle ,	
					ot_tranpr       =>  :szTranPr,
					ot_result       =>  :otResult,
					ot_rstmsg       =>  :szSqlErrorMsg);
			END;
			END-EXEC;

			logmessage(
					"msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]szTranPr[%s]rst[%d]msg[]charge[%s]channel[%s]tmplCd[%s]replacemsg[]szSenderKey[%s]",
					sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,szTranPr,otResult/*,szSndmsg*/,szCharge,szChannel,szTmplCd/*,szReplacemsg*/,szSenderKey);

			if( otResult != 0 )
			{
				if( otResult == -999 )
				{
					monitoring("proc_set_atalk_msgdata szTranPr[%s]dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
							szTranPr,
							mappingfile.getDstAddr(),
							sqlca.sqlcode,
							sqlca.sqlerrm.sqlerrmc 
						  );
					return -2;
				}

				monitoring("proc_set_atalk_msgdata szTranPr[%s]dstaddr[%s]otReuslt[%d]errMsg[%s]", 
						szTranPr,
						mappingfile.getDstAddr(),
						otResult,
						trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
				return 58;
			}
		}

		// NaverTalkTalk : 11
		else if (nChannel == 11)
		{
			EXEC SQL CONTEXT USE :db;
			EXEC SQL EXECUTE
				BEGIN			
				proc_set_msgdata_ntk(
					ptn_id       =>    :nPID,
					telco_id     =>    :nTelco,
					ntk_telco_id =>    :nNtkTelco,
					ptn_sn       =>    :szSerial,
					dstaddr      =>    :szDstadr,
					callback     =>    :szCalbck,
					msg_body     =>    :szAtalkmsg,
					resv_data    =>    :szReserve,
					priority     =>    :nPRT,
					job_code     =>    :nJOB,
					tmpl_cd		 =>	   :szTmplCd,
					replace_msg  =>	   :szReplacemsg,
					sender_key   =>    :szSenderKey,
					msgtitle	 =>    :szTitle,
					button       =>    :szButton,
					ot_mmsid     =>    :nNtkId,
					ot_result    =>    :otResult,
					ot_rstmsg    =>    :szSqlErrorMsg
					);			
			END;
			END-EXEC;

			logmessage(	"msgType[%d] pid[%d] tel[%d] ptnsn[%s] cal[%s] resv[%s] "
				"prt[%d] job[%d] nNtkId[%lld] rst[%d] msg[] channel[%s] "
				"tmplCd[%s] replacemsg[] szSenderKey[%s] ",
				sndGroupInfo.nMsgType, nPID, nTelco, szSerial, szCalbck, szReserve,
				nPRT, nJOB, nNtkId,	otResult /*,szAtalkmsg*/, szChannel,
				szTmplCd/*,szReplacemsg*/, szSenderKey);

			if( otResult != 0 )
			{
				if( otResult == -999 )
				{
					monitoring("proc_set_msgdata_ntk nNtkId[%lld] dstaddr[%s] sqlcode[%d] sqlmsg[%s]",
							nNtkId,
							mappingfile.getDstAddr(),
							sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
					return -2;
				}

				monitoring("proc_set_msgdata_ntk nNtkId[%lld] dstaddr[%s] otReuslt[%d] errMsg[%s]", 
						nNtkId,
						mappingfile.getDstAddr(),
						otResult,
						trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
				return 58;
			}
		}
			
		//else if(atoi(szChannel) == 8)   
		else if(nChannel == 8)   
		{
			EXEC SQL CONTEXT USE :db;
			EXEC SQL EXECUTE
				BEGIN
				PROC_SET_WTALK_MSG(
						dstaddr         =>  :szDstadr,
						callback        =>  :szCalbck,
						msg_body        =>  :szAtalkmsg,
						resv_data       =>  :szReserve,
						ptn_sn          =>  :szSerial,	
						job_code        =>  :szJob,
						charge          =>	:szCharge,
						tmplCd          =>	:szTmplCd,
						replace_msg     =>	:szReplacemsg,
						sender_key      =>  :szSenderKey ,
						req_date        =>  :szReqDate,
						ot_tranpr       =>  :szTranPr,
						ot_result       =>  :otResult,
						ot_rstmsg       =>  :szSqlErrorMsg
						);
			END;
			END-EXEC;

			logmessage(
					"msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]szTranPr[%s]rst[%d]msg[]charge[%s]channel[%s]tmplCd[%s]replacemsg[]szSenderKey[%s]",
					sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,szTranPr,otResult/*,szSndmsg*/,szCharge,szChannel,szTmplCd/*,szReplacemsg*/,szSenderKey);

			if( otResult != 0 )	{
				if( otResult == -999 )	{
					monitoring("PROC_SET_WTALK_MSG szTranPr[%s]dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
							szTranPr,
							mappingfile.getDstAddr(),
							sqlca.sqlcode,
							sqlca.sqlerrm.sqlerrmc 
						  );
					return -2;
				}

				monitoring("PROC_SET_WTALK_MSG szTranPr[%s]dstaddr[%s]otReuslt[%d]errMsg[%s]", 
						szTranPr,
						mappingfile.getDstAddr(),
						otResult,
						trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
				return 58;
			}
		}
		else
		{
			//if(atoi(szChannel) == 5)
			if(nChannel == 5) {
				logmessage(
						"msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]msgid[%d]rst[%d]msg[]charge[%s]channel[%s]toss",
						sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nMsgId,otResult/*,szSndmsg*/,szCharge,szChannel);
			}
			//else //20210629 channel condition add
			//else if(atoi(szChannel) == 1 || atoi(szChannel) == 2|| atoi(szChannel) == 3|| atoi(szChannel) == 4)
			// RCS : 1
			else if(nChannel == 1 || nChannel == 2|| nChannel == 3|| nChannel == 4)   
			{
				if((strcmp(szCalbck,"15884000") == 0 || strcmp(szCalbck,"15889955")== 0) && strlen(szMbaseId) > 0)
				{
					EXEC SQL CONTEXT USE :db;
					EXEC SQL EXECUTE
						BEGIN
						proc_set_msgdata_rcs(
								ptn_id       =>    :nPID,
								telco_id     =>    :nTelco ,
								rcs_telco_id =>    :nRcsTelco ,
								ptn_sn       =>    :szSerial,
								dstaddr      =>    :szDstadr,
								callback     =>    :szCalbck,
								msg_body     =>    :szRcsmsg,
								resv_data    =>    :szReserve,
								priority     =>    :nPRT,
								job_code     =>    :nJOB,
								mbase_id     =>    :szMbaseId,
								rcs_group_id =>    :szRcsGroupId,	
								head     	 =>    :szHead,	
								footer     	 =>    :szFooter,	
								replace_msg  =>	   :szReplacemsg,   				                	
								ot_mmsid     =>    :nRcsId,
								ot_result    =>    :otResult,
								ot_rstmsg    =>    :szSqlErrorMsg);
					END;
					END-EXEC;

					logmessage(
							"msgType[%d]pid[%d]tel[%d]rcstel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]rcsid[%lld]rst[%d]msg[]charge[%s]channel[%s]",
							sndGroupInfo.nMsgType,nPID,nTelco,nRcsTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nRcsId,otResult/*,szRcsmsg*/,szCharge,szChannel);

					if( otResult != 0 )	{
						if( otResult == -999 ) {
							monitoring("proc_set_msgdata_rcs nRcsId[%lld]dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
									nRcsId,
									mappingfile.getDstAddr(),
									sqlca.sqlcode,
									sqlca.sqlerrm.sqlerrmc 
								  );
							return -2;
						}

						monitoring("proc_set_msgdata_rcs nRcsId[%lld]dstaddr[%s]otReuslt[%d]errMsg[%s]", 
								nRcsId,
								mappingfile.getDstAddr(),
								otResult,
								trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
						return 58;
					}
				}
				else
				{
					EXEC SQL CONTEXT USE :db;
					EXEC SQL EXECUTE
						BEGIN
						//proc_set_msgdata_ext2(
						proc_set_msgdata_key_ext2(
								ptn_id       =>    :nPID,
								telco_id     =>    :nTelco ,
								ptn_sn       =>    :szSerial,
								dstaddr      =>    :szDstadr,
								callback     =>    :szCalbck,
								msg_body     =>    :szSndmsg,
								resv_data    =>    :szReserve,
								priority     =>    :nPRT,
								job_code     =>    :nJOB,
								id_code		 =>	   :szIdCode,
								ot_msgid     =>    :nMsgId,
								ot_result    =>    :otResult,
								ot_rstmsg    =>    :szSqlErrorMsg);
					END;
					END-EXEC;

					logmessage(
							"2msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]msgid[%d]rst[%d]msg[]charge[%s]channel[%s] idcode[%s]",
							sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nMsgId,otResult/*,szSndmsg*/,szCharge,szChannel, szIdCode);

					if( otResult != 0 )
					{
						if( otResult == -999 )
						{
							monitoring("2proc_set_msgdata_ext2 nMsgId[%d]dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
									nMsgId,
									mappingfile.getDstAddr(),
									sqlca.sqlcode,
									sqlca.sqlerrm.sqlerrmc 
								  );
							return -2;
						}

						monitoring("2proc_set_msgdata_ext2 nMsgId[%d]dstaddr[%s]otReuslt[%d]errMsg[%s]", 
								nMsgId,
								mappingfile.getDstAddr(),
								otResult,
								trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
						return 58;
					}
				}
			}
			// SMS : 10
			else
			{
				EXEC SQL CONTEXT USE :db;
				EXEC SQL EXECUTE
					BEGIN
					//proc_set_msgdata_ext2(
					proc_set_msgdata_key_ext2(
							ptn_id       =>    :nPID,
							telco_id     =>    :nTelco ,
							ptn_sn       =>    :szSerial,
							dstaddr      =>    :szDstadr,
							callback     =>    :szCalbck,
							msg_body     =>    :szSndmsg,
							resv_data    =>    :szReserve,
							priority     =>    :nPRT,
							job_code     =>    :nJOB,
							id_code		 =>	   :szIdCode,
							ot_msgid     =>    :nMsgId,
							ot_result    =>    :otResult,
							ot_rstmsg    =>    :szSqlErrorMsg);
				END;
				END-EXEC;

				logmessage(
						"msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]msgid[%d]rst[%d]msg[]charge[%s]channel[%s] idcode[%s]",
						sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nMsgId,otResult/*,szSndmsg*/,szCharge,szChannel, szIdCode);

				if( otResult != 0 )
				{
					if( otResult == -999 )
					{
						monitoring("proc_set_msgdata_ext2 nMsgId[%d]dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
								nMsgId,
								mappingfile.getDstAddr(),
								sqlca.sqlcode,
								sqlca.sqlerrm.sqlerrmc 
							  );
						return -2;
					}

					monitoring("proc_set_msgdata_ext2 nMsgId[%d]dstaddr[%s]otReuslt[%d]errMsg[%s]", 
							nMsgId,
							mappingfile.getDstAddr(),
							otResult,
							trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
					return 58;
				}
			}
		}
	} 
	else 
	{
		EXEC SQL CONTEXT USE :db;
		EXEC SQL EXECUTE
			BEGIN
			//proc_set_urldata_ext2(
			proc_set_urldata_key_ext2(
					ptn_id      =>    :nPID,
					telco_id    =>    :nTelco ,
					ptn_sn   	=>    :szSerial,
					dstaddr     =>    :szDstadr,
					callback    =>    :szCalbck,
					msg_body    =>    :szSndmsg,
					resv_data   =>    :szReserve,
					priority    =>    :nPRT,
					job_code    =>    :nJOB,
					id_code		 =>	   :szIdCode,
					ot_msgid    =>    :nMsgId,
					ot_result   =>    :otResult,
					ot_errm     =>    :szSqlErrorMsg);
		END;
		END-EXEC;

		logmessage(
				"0msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]msgid[%d]rst[%d]msg[]charge[%s]channel[%s]",
				sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nMsgId,otResult/*,szSndmsg*/,szCharge,szChannel);

		if( otResult != 0 )
		{
			if( otResult == -999 )
			{
				monitoring("proc_set_urldata_ext2 nMsgId[%d]dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
						nMsgId,
						mappingfile.getDstAddr(),
						sqlca.sqlcode,
						sqlca.sqlerrm.sqlerrmc 
					  );
				return -2;
			}

			monitoring("proc_set_urldata_ext2 nMsgId[%d]dstaddr[%s]otReuslt[%d]errMsg[%s]", 
					nMsgId,
					mappingfile.getDstAddr(),
					otResult,
					trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
			return 58;
		}    
	}

	//if(atoi(szChannel) >0 && atoi(szChannel) <=5)
	//{
	//	logmessage(
	//        "msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]msgid[%d]szTranPr[%s]rst[%d]msg[%s]charge[%s]channel[%s]tmplCd[%s]replacemsg[%s]szSenderKey[%s]",
	//        sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nMsgId,szTranPr,otResult,szSndmsg,szCharge,szChannel,szTmplCd,szReplacemsg,szSenderKey);
	/*}
	  else if(atoi(szChannel) == 6 || atoi(szChannel) == 7)   
	  {
	  logmessage(
	  "msgType[%d]pid[%d]tel[%d]ptnsn[%s]cal[%s]resv[%s]prt[%d]job[%d]msgid[%d]rst[%d]msg[%s]charge[%s]channel[%s]tmplCd[%s]replacemsg[%s]",
	  sndGroupInfo.nMsgType,nPID,nTelco,szSerial,szCalbck,szReserve,nPRT,nJOB,nMsgId,otResult,szSndmsg,szCharge,szChannel,szTmplCd,szReplacemsg);
	  }


	  if( otResult != 0 )
	  {
	  if( otResult == -999 )
	  {
	  monitoring("proc_set_msgdata dstaddr[%s]sqlcode[%d] sqlmsg[%s]", 
	  mappingfile.getDstAddr(),
	  sqlca.sqlcode,
	  sqlca.sqlerrm.sqlerrmc 
	  );
	  return -2;
	  }

	  monitoring("proc_set_msgdata dstaddr[%s]otReuslt[%d]errMsg[%s]", 
	  mappingfile.getDstAddr(),
	  otResult,
	  trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
	  return 58;
	  }*/

	logmessage("[SHS]setMsgData start2[%d][%s]",groupInfo.f_grp_id,szSerial);

	return 0;
}

int getTelcoId(char* szTelco,char* szDstaddr,int nMsgType)
{
    char* p;
    int telcoArray[7];  
    char CID[4];
    int i=0;
    memset(telcoArray,0x00,sizeof(telcoArray));

    p = strtok(szTelco,"|");
    if( p == NULL ) {
	if(nMsgType==1)
		return gConf.telcoCallbackDefault;
	else
		return gConf.telcoDefault;
    }
    telcoArray[0] = atoi(p);

    while(p = strtok(NULL,"|") )
    {
        telcoArray[++i]= atoi(p);
        if( i > 6 ) break;
    }
/*    if( i != 6 ) return gConf.telcoDefaultID;
*/

    CCL(CID);
    memcpy(CID,szDstaddr,3);

    switch(atoi(CID))
    {
        case 11 :
            return telcoArray[0];

        case 16 :
            return telcoArray[1];

        case 17 :
            return telcoArray[2];

        case 18 :
            return telcoArray[3];

        case 19 :
            return telcoArray[4];

        case 10 :
            return telcoArray[5];

        case 50 :
            return telcoArray[6];
    }
/* default telco 010 */
/* edit 2008.11.17 default 050 : dacom for 013 */
    //return telcoArray[6];
    return telcoArray[5];
}

/* add 2009/09/22 */
char* cutString(char* szOrg, int leng)
{
    int i = 0,j=0,k=0;

    if(strlen(szOrg) <= leng) {
        return szOrg;
    } else {
        j=0;
        for(i=leng-1;i>=0;i--) {
            if((unsigned char)szOrg[i] <= 0x7f ) break;    // ko : start 0x80
            j++;
        }
        if( j % 2 == 1) k=leng-1;
        else k=leng;
        for(i=strlen(szOrg);i>=k;i--){
            szOrg[i] = 0x00;
        }
        return szOrg;
    }
}
